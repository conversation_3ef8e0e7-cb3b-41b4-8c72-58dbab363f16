plugins {
    id 'org.jetbrains.kotlin.jvm' version '2.1.0'
    id 'org.jetbrains.kotlin.plugin.spring' version '2.1.0'
    id 'org.jetbrains.kotlin.plugin.jpa' version '2.1.0'
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.outbook'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot 核心依赖
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-aop'

    // 数据库相关
    implementation 'com.baomidou:mybatis-plus-spring-boot3-starter:3.5.7'
    implementation 'com.baomidou:mybatis-plus-generator:3.5.7'
    implementation 'org.flywaydb:flyway-core'
    implementation 'org.flywaydb:flyway-mysql'
    runtimeOnly 'com.mysql:mysql-connector-j'
    runtimeOnly 'com.h2database:h2'

    // 认证和权限
    implementation 'cn.dev33:sa-token-spring-boot3-starter:1.37.0'
    implementation 'cn.dev33:sa-token-redis-jackson:1.37.0'

    // JSON处理
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // Kotlin相关
    implementation 'org.jetbrains.kotlin:kotlin-reflect'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-reactor'

    // 工具类
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'cn.hutool:hutool-all:5.8.25'

    // 测试依赖
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.jetbrains.kotlin:kotlin-test-junit5'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

kotlin {
    compilerOptions {
        freeCompilerArgs.addAll '-Xjsr305=strict'
    }
}

tasks.named('test') {
    useJUnitPlatform()
}
