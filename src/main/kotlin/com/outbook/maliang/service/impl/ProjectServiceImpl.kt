package com.outbook.maliang.service.impl

import cn.dev33.satoken.stp.StpUtil
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.outbook.maliang.common.exception.BusinessException
import com.outbook.maliang.dto.request.CreateProjectRequest
import com.outbook.maliang.dto.request.ProjectQueryRequest
import com.outbook.maliang.dto.request.UpdateProjectRequest
import com.outbook.maliang.dto.response.CollaboratorResponse
import com.outbook.maliang.dto.response.ProjectDetailResponse
import com.outbook.maliang.dto.response.ProjectListResponse
import com.outbook.maliang.dto.response.ProjectPermissions
import com.outbook.maliang.dto.response.ProjectResponse
import com.outbook.maliang.entity.Project
import com.outbook.maliang.entity.ProjectCollaborator
import com.outbook.maliang.enums.CollaboratorStatus
import com.outbook.maliang.enums.ProjectRole
import com.outbook.maliang.mapper.ProjectCollaboratorMapper
import com.outbook.maliang.mapper.ProjectMapper
import com.outbook.maliang.service.ProjectService
import com.outbook.maliang.service.UserService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 项目服务实现类
 */
@Service
class ProjectServiceImpl(
    private val projectMapper: ProjectMapper,
    private val projectCollaboratorMapper: ProjectCollaboratorMapper,
    private val userService: UserService
) : ServiceImpl<ProjectMapper, Project>(), ProjectService {

    override fun getUserAccessibleProjects(userId: Long): List<Project> {
        return projectMapper.selectUserOwnedProjects(userId)
    }

    /**
     * 分页查询用户可访问的项目
     */
    override fun getUserAccessibleProjects(queryRequest: ProjectQueryRequest, userId: Long): ProjectListResponse {
        val page = Page<Project>(queryRequest.page.toLong(), queryRequest.size.toLong())
        val resultPage = projectMapper.selectUserAccessibleProjects(page, userId, queryRequest.keyword)

        val projectResponses = resultPage.records.map { project ->
            val ownerName = userService.getById(project.ownerId)?.nickname ?: "未知用户"
            val userRole = getUserProjectRole(project.id!!, userId)
            ProjectResponse.fromEntity(project, ownerName, userRole)
        }

        return ProjectListResponse(
            records = projectResponses,
            total = resultPage.total,
            size = resultPage.size,
            current = resultPage.current,
            pages = resultPage.pages
        )
    }

    @Transactional
    override fun createProject(name: String, description: String?, ownerId: Long): Project {
        val project = Project(
            name = name,
            description = description,
            ownerId = ownerId
        )
        save(project)

        // 自动为项目所有者添加OWNER角色的协作者记录
        val ownerCollaborator = ProjectCollaborator(
            projectId = project.id!!,
            userId = ownerId,
            role = ProjectRole.OWNER.code,
            status = CollaboratorStatus.ACCEPTED.code,
            invitedBy = ownerId,
            joinedAt = LocalDateTime.now()
        )
        projectCollaboratorMapper.insert(ownerCollaborator)

        return project
    }

    /**
     * 创建项目（使用DTO）
     */
    @Transactional
    override fun createProject(request: CreateProjectRequest, ownerId: Long): ProjectResponse {
        val project = createProject(request.name, request.description, ownerId)

        // 如果提供了封面图URL，更新项目
        if (!request.coverUrl.isNullOrBlank()) {
            val updatedProject = project.copy(
                coverUrl = request.coverUrl,
                visibility = request.visibility
            )
            updateById(updatedProject)
        }

        val ownerName = userService.getById(ownerId)?.nickname ?: "未知用户"
        return ProjectResponse.fromEntity(project, ownerName, ProjectRole.OWNER.code)
    }

    override fun hasProjectAccess(projectId: Long, userId: Long): Boolean {
        val role = projectMapper.checkUserProjectAccess(projectId, userId)
        return role != null
    }

    /**
     * 获取用户在项目中的角色
     */
    override fun getUserProjectRole(projectId: Long, userId: Long): String? {
        return projectMapper.checkUserProjectAccess(projectId, userId)
    }

    /**
     * 检查用户是否有特定权限
     */
    override fun checkUserPermission(projectId: Long, userId: Long, requiredPermission: String): Boolean {
        val userRole = getUserProjectRole(projectId, userId) ?: return false

        return when (requiredPermission) {
            "EDIT" -> ProjectRole.hasEditPermission(userRole)
            "MANAGE" -> ProjectRole.hasManagePermission(userRole)
            "DELETE" -> ProjectRole.hasDeletePermission(userRole)
            else -> false
        }
    }

    override fun getProjectCollaboratorsRaw(projectId: Long): List<Map<String, Any>> {
        return projectCollaboratorMapper.selectProjectCollaboratorsWithUserInfo(projectId)
    }

    /**
     * 获取项目协作者列表（返回DTO）
     */
    override fun getProjectCollaborators(projectId: Long): List<CollaboratorResponse> {
        val collaborators = projectCollaboratorMapper.selectProjectCollaboratorsWithUserInfo(projectId)

        return collaborators.map { collaborator ->
            CollaboratorResponse(
                id = collaborator["id"] as Long,
                userId = collaborator["user_id"] as Long,
                username = collaborator["username"] as? String,
                nickname = collaborator["nickname"] as? String,
                email = collaborator["email"] as? String,
                avatarUrl = collaborator["avatar_url"] as? String,
                role = collaborator["role"] as String,
                permissions = collaborator["permissions"] as? String,
                status = collaborator["status"] as String,
                invitedBy = collaborator["invited_by"] as? Long,
                invitedByName = collaborator["invited_by_name"] as? String,
                joinedAt = collaborator["joined_at"] as? LocalDateTime,
                createdAt = collaborator["created_at"] as? LocalDateTime
            )
        }
    }

    @Transactional
    override fun addCollaborator(projectId: Long, userId: Long, role: String, invitedBy: Long): Boolean {
        // 检查项目是否存在
        val project = getById(projectId) ?: throw BusinessException("项目不存在", 404)

        // 检查邀请者是否有管理权限
        if (!checkUserPermission(projectId, invitedBy, "MANAGE")) {
            throw BusinessException("无权限添加协作者", 403)
        }

        // 检查用户是否已经是协作者
        val existingCollaborator = projectCollaboratorMapper.findByProjectIdAndUserId(projectId, userId)
        if (existingCollaborator != null) {
            throw BusinessException("用户已经是项目协作者", 400)
        }

        // 验证角色
        val projectRole = ProjectRole.fromCode(role) ?: throw BusinessException("无效的角色", 400)

        // 创建协作者记录
        val collaborator = ProjectCollaborator(
            projectId = projectId,
            userId = userId,
            role = projectRole.code,
            status = CollaboratorStatus.PENDING.code,
            invitedBy = invitedBy
        )

        return projectCollaboratorMapper.insert(collaborator) > 0
    }

    /**
     * 更新协作者角色
     */
    @Transactional
    override fun updateCollaborator(projectId: Long, userId: Long, role: String, operatorId: Long): Boolean {
        // 检查操作者权限
        if (!checkUserPermission(projectId, operatorId, "MANAGE")) {
            throw BusinessException("无权限管理协作者", 403)
        }

        // 检查协作者是否存在
        val collaborator = projectCollaboratorMapper.findByProjectIdAndUserId(projectId, userId)
            ?: throw BusinessException("协作者不存在", 404)

        // 验证角色
        val projectRole = ProjectRole.fromCode(role) ?: throw BusinessException("无效的角色", 400)

        // 不能修改项目所有者的角色
        if (collaborator.role == ProjectRole.OWNER.code && operatorId != userId) {
            throw BusinessException("不能修改项目所有者的角色", 403)
        }

        // 更新协作者角色
        val updatedCollaborator = collaborator.copy(role = projectRole.code)
        return projectCollaboratorMapper.updateById(updatedCollaborator) > 0
    }

    /**
     * 移除协作者
     */
    @Transactional
    override fun removeCollaborator(projectId: Long, userId: Long, operatorId: Long): Boolean {
        // 检查操作者权限
        if (!checkUserPermission(projectId, operatorId, "MANAGE")) {
            throw BusinessException("无权限管理协作者", 403)
        }

        // 检查协作者是否存在
        val collaborator = projectCollaboratorMapper.findByProjectIdAndUserId(projectId, userId)
            ?: throw BusinessException("协作者不存在", 404)

        // 不能移除项目所有者
        if (collaborator.role == ProjectRole.OWNER.code) {
            throw BusinessException("不能移除项目所有者", 403)
        }

        // 删除协作者
        return projectCollaboratorMapper.deleteById(collaborator.id!!) > 0
    }

    /**
     * 接受协作邀请
     */
    @Transactional
    override fun acceptCollaboratorInvitation(projectId: Long, userId: Long): Boolean {
        val collaborator = projectCollaboratorMapper.findByProjectIdAndUserId(projectId, userId)
            ?: throw BusinessException("邀请不存在", 404)

        if (collaborator.status != CollaboratorStatus.PENDING.code) {
            throw BusinessException("邀请状态无效", 400)
        }

        return projectCollaboratorMapper.updateCollaboratorStatus(
            projectId, userId, CollaboratorStatus.ACCEPTED.code
        ) > 0
    }

    /**
     * 拒绝协作邀请
     */
    @Transactional
    override fun rejectCollaboratorInvitation(projectId: Long, userId: Long): Boolean {
        val collaborator = projectCollaboratorMapper.findByProjectIdAndUserId(projectId, userId)
            ?: throw BusinessException("邀请不存在", 404)

        if (collaborator.status != CollaboratorStatus.PENDING.code) {
            throw BusinessException("邀请状态无效", 400)
        }

        return projectCollaboratorMapper.updateCollaboratorStatus(
            projectId, userId, CollaboratorStatus.REJECTED.code
        ) > 0
    }

    /**
     * 获取项目详情
     */
    override fun getProjectDetail(projectId: Long, userId: Long): ProjectDetailResponse {
        // 检查访问权限
        if (!hasProjectAccess(projectId, userId)) {
            throw BusinessException("无权限访问该项目", 403)
        }

        // 获取项目信息
        val projectData = projectMapper.selectProjectWithUserRole(projectId, userId)
            ?: throw BusinessException("项目不存在", 404)

        val project = getById(projectId)!!
        val userRole = projectData["user_role"] as? String
        val ownerName = projectData["owner_name"] as? String

        val projectResponse = ProjectResponse.fromEntity(project, ownerName, userRole)

        // 获取协作者列表
        val collaborators = getProjectCollaborators(projectId)

        // 计算用户权限
        val permissions = calculateUserPermissions(userRole)

        return ProjectDetailResponse(
            project = projectResponse,
            collaborators = collaborators,
            permissions = permissions
        )
    }

    /**
     * 更新项目信息
     */
    @Transactional
    override fun updateProject(projectId: Long, request: UpdateProjectRequest, userId: Long): ProjectResponse {
        // 检查编辑权限
        if (!checkUserPermission(projectId, userId, "EDIT")) {
            throw BusinessException("无权限编辑该项目", 403)
        }

        val project = getById(projectId) ?: throw BusinessException("项目不存在", 404)

        // 构建更新的项目对象
        val updatedProject = project.copy(
            name = request.name ?: project.name,
            description = request.description ?: project.description,
            visibility = request.visibility ?: project.visibility,
            coverUrl = request.coverUrl ?: project.coverUrl
        )

        updateById(updatedProject)

        // 更新最后访问时间
        projectMapper.updateLastAccessedTime(projectId)

        val ownerName = userService.getById(project.ownerId)?.nickname ?: "未知用户"
        val userRole = getUserProjectRole(projectId, userId)

        return ProjectResponse.fromEntity(updatedProject, ownerName, userRole)
    }

    /**
     * 删除项目
     */
    @Transactional
    override fun deleteProject(projectId: Long, userId: Long): Boolean {
        // 检查删除权限
        if (!checkUserPermission(projectId, userId, "DELETE")) {
            throw BusinessException("无权限删除该项目", 403)
        }

        // 删除项目的所有协作者
        projectCollaboratorMapper.deleteByProjectId(projectId)

        // 软删除项目
        return removeById(projectId)
    }

    /**
     * 更新项目最后访问时间
     */
    override fun updateLastAccessTime(projectId: Long): Boolean {
        return projectMapper.updateLastAccessedTime(projectId) > 0
    }

    /**
     * 计算用户权限
     */
    private fun calculateUserPermissions(userRole: String?): ProjectPermissions {
        return ProjectPermissions(
            canEdit = userRole?.let { ProjectRole.hasEditPermission(it) } ?: false,
            canDelete = userRole?.let { ProjectRole.hasDeletePermission(it) } ?: false,
            canManageCollaborators = userRole?.let { ProjectRole.hasManagePermission(it) } ?: false,
            canCreateCanvas = userRole?.let { ProjectRole.hasEditPermission(it) } ?: false,
            canViewCollaborators = userRole != null
        )
    }
}
