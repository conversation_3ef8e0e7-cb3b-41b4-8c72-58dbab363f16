package com.outbook.maliang.service

import com.baomidou.mybatisplus.extension.service.IService
import com.outbook.maliang.dto.request.CreateProjectRequest
import com.outbook.maliang.dto.request.ProjectQueryRequest
import com.outbook.maliang.dto.request.UpdateProjectRequest
import com.outbook.maliang.dto.response.CollaboratorResponse
import com.outbook.maliang.dto.response.ProjectDetailResponse
import com.outbook.maliang.dto.response.ProjectListResponse
import com.outbook.maliang.dto.response.ProjectResponse
import com.outbook.maliang.entity.Project

/**
 * 项目服务接口
 */
interface ProjectService : IService<Project> {

    /**
     * 获取用户可访问的项目列表
     */
    fun getUserAccessibleProjects(userId: Long): List<Project>

    /**
     * 分页查询用户可访问的项目
     */
    fun getUserAccessibleProjects(queryRequest: ProjectQueryRequest, userId: Long): ProjectListResponse

    /**
     * 创建项目
     */
    fun createProject(name: String, description: String?, ownerId: Long): Project

    /**
     * 创建项目（使用DTO）
     */
    fun createProject(request: CreateProjectRequest, ownerId: Long): ProjectResponse

    /**
     * 检查用户是否有项目访问权限
     */
    fun hasProjectAccess(projectId: Long, userId: Long): Boolean

    /**
     * 获取用户在项目中的角色
     */
    fun getUserProjectRole(projectId: Long, userId: Long): String?

    /**
     * 检查用户是否有特定权限
     */
    fun checkUserPermission(projectId: Long, userId: Long, requiredPermission: String): Boolean

    /**
     * 获取项目协作者列表（原始数据）
     */
    fun getProjectCollaboratorsRaw(projectId: Long): List<Map<String, Any>>

    /**
     * 获取项目协作者列表（返回DTO）
     */
    fun getProjectCollaborators(projectId: Long): List<CollaboratorResponse>

    /**
     * 添加项目协作者
     */
    fun addCollaborator(projectId: Long, userId: Long, role: String, invitedBy: Long): Boolean

    /**
     * 更新协作者角色
     */
    fun updateCollaborator(projectId: Long, userId: Long, role: String, operatorId: Long): Boolean

    /**
     * 移除协作者
     */
    fun removeCollaborator(projectId: Long, userId: Long, operatorId: Long): Boolean

    /**
     * 接受协作邀请
     */
    fun acceptCollaboratorInvitation(projectId: Long, userId: Long): Boolean

    /**
     * 拒绝协作邀请
     */
    fun rejectCollaboratorInvitation(projectId: Long, userId: Long): Boolean

    /**
     * 获取项目详情
     */
    fun getProjectDetail(projectId: Long, userId: Long): ProjectDetailResponse

    /**
     * 更新项目信息
     */
    fun updateProject(projectId: Long, request: UpdateProjectRequest, userId: Long): ProjectResponse

    /**
     * 删除项目
     */
    fun deleteProject(projectId: Long, userId: Long): Boolean

    /**
     * 更新项目最后访问时间
     */
    fun updateLastAccessTime(projectId: Long): Boolean
}
