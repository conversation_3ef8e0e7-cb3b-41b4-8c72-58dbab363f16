package com.outbook.maliang.annotation

/**
 * 项目权限检查注解
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class ProjectPermission(
    /**
     * 需要的权限类型
     * VIEW - 查看权限
     * EDIT - 编辑权限
     * MANAGE - 管理权限（添加/删除协作者）
     * DELETE - 删除权限
     */
    val value: String,
    
    /**
     * 项目ID参数名，默认为 "projectId"
     */
    val projectIdParam: String = "projectId"
)
