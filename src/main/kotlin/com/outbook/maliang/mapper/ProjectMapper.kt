package com.outbook.maliang.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.outbook.maliang.entity.Project
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param

/**
 * 项目Mapper接口
 */
@Mapper
interface ProjectMapper : BaseMapper<Project> {
    
    /**
     * 分页查询用户可访问的项目
     */
    fun selectUserAccessibleProjects(
        page: Page<Project>,
        @Param("userId") userId: Long,
        @Param("keyword") keyword: String?
    ): IPage<Project>

    /**
     * 获取项目详情（包含用户角色）
     */
    fun selectProjectWithUserRole(
        @Param("projectId") projectId: Long,
        @Param("userId") userId: Long
    ): Map<String, Any>?

    /**
     * 检查用户对项目的访问权限
     */
    fun checkUserProjectAccess(
        @Param("projectId") projectId: Long,
        @Param("userId") userId: Long
    ): String?
    
    /**
     * 查询用户拥有的项目
     */
    fun selectUserOwnedProjects(@Param("userId") userId: Long): List<Project>
    
    /**
     * 查询用户协作的项目
     */
    fun selectUserCollaboratedProjects(@Param("userId") userId: Long): List<Project>
    
    /**
     * 更新项目最后访问时间
     */
    fun updateLastAccessedTime(@Param("projectId") projectId: Long): Int
}
