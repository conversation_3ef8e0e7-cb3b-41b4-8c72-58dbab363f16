package com.outbook.maliang.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.outbook.maliang.entity.ProjectCollaborator
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param

/**
 * 项目协作者Mapper接口
 */
@Mapper
interface ProjectCollaboratorMapper : BaseMapper<ProjectCollaborator> {
    
    /**
     * 根据项目ID和用户ID查找协作者
     */
    fun findByProjectIdAndUserId(
        @Param("projectId") projectId: Long,
        @Param("userId") userId: Long
    ): ProjectCollaborator?
    
    /**
     * 获取项目的所有协作者（包含用户信息）
     */
    fun selectProjectCollaboratorsWithUserInfo(@Param("projectId") projectId: Long): List<Map<String, Any>>
    
    /**
     * 获取用户协作的项目ID列表
     */
    fun selectUserCollaboratedProjectIds(@Param("userId") userId: Long): List<Long>
    
    /**
     * 统计项目的协作者数量
     */
    fun countProjectCollaborators(@Param("projectId") projectId: Long): Int
    
    /**
     * 删除项目的所有协作者
     */
    fun deleteByProjectId(@Param("projectId") projectId: Long): Int
    
    /**
     * 更新协作者状态
     */
    fun updateCollaboratorStatus(
        @Param("projectId") projectId: Long,
        @Param("userId") userId: Long,
        @Param("status") status: String
    ): Int
    
    /**
     * 检查用户是否是项目协作者
     */
    fun isProjectCollaborator(
        @Param("projectId") projectId: Long,
        @Param("userId") userId: Long
    ): Boolean
}
