package com.outbook.maliang.controller

import cn.dev33.satoken.stp.StpUtil
import com.outbook.maliang.common.result.Result
import com.outbook.maliang.dto.request.AddCollaboratorRequest
import com.outbook.maliang.dto.request.CreateProjectRequest
import com.outbook.maliang.dto.request.ProjectQueryRequest
import com.outbook.maliang.dto.request.UpdateCollaboratorRequest
import com.outbook.maliang.dto.request.UpdateProjectRequest
import com.outbook.maliang.dto.response.CollaboratorResponse
import com.outbook.maliang.dto.response.ProjectDetailResponse
import com.outbook.maliang.dto.response.ProjectListResponse
import com.outbook.maliang.dto.response.ProjectResponse
import com.outbook.maliang.service.impl.ProjectServiceImpl
import org.springframework.web.bind.annotation.*
import jakarta.validation.Valid

/**
 * 项目管理控制器
 */
@RestController
@RequestMapping("/api/projects")
class ProjectController(
    private val projectService: ProjectServiceImpl
) {

    /**
     * 创建项目
     */
    @PostMapping
    fun createProject(@Valid @RequestBody request: CreateProjectRequest): Result<ProjectResponse> {
        val userId = StpUtil.getLoginIdAsLong()
        val project = projectService.createProject(request, userId)
        return Result.success(project, "项目创建成功")
    }

    /**
     * 获取项目列表（分页查询）
     */
    @GetMapping
    fun getProjects(
        @RequestParam(defaultValue = "1") page: Int,
        @RequestParam(defaultValue = "10") size: Int,
        @RequestParam(required = false) keyword: String?,
        @RequestParam(required = false) visibility: String?,
        @RequestParam(required = false) status: String?,
        @RequestParam(defaultValue = "updatedAt") sortBy: String,
        @RequestParam(defaultValue = "desc") sortOrder: String
    ): Result<ProjectListResponse> {
        val userId = StpUtil.getLoginIdAsLong()
        val queryRequest = ProjectQueryRequest(
            page = page,
            size = size,
            keyword = keyword,
            visibility = visibility,
            status = status,
            sortBy = sortBy,
            sortOrder = sortOrder
        )
        val projects = projectService.getUserAccessibleProjects(queryRequest, userId)
        return Result.success(projects)
    }

    /**
     * 获取项目详情
     */
    @GetMapping("/{projectId}")
    fun getProjectDetail(@PathVariable projectId: Long): Result<ProjectDetailResponse> {
        val userId = StpUtil.getLoginIdAsLong()
        val projectDetail = projectService.getProjectDetail(projectId, userId)
        return Result.success(projectDetail)
    }

    /**
     * 更新项目信息
     */
    @PutMapping("/{projectId}")
    fun updateProject(
        @PathVariable projectId: Long,
        @Valid @RequestBody request: UpdateProjectRequest
    ): Result<ProjectResponse> {
        val userId = StpUtil.getLoginIdAsLong()
        val project = projectService.updateProject(projectId, request, userId)
        return Result.success(project, "项目更新成功")
    }

    /**
     * 删除项目
     */
    @DeleteMapping("/{projectId}")
    fun deleteProject(@PathVariable projectId: Long): Result<Nothing> {
        val userId = StpUtil.getLoginIdAsLong()
        projectService.deleteProject(projectId, userId)
        return Result.success(message = "项目删除成功")
    }

    /**
     * 获取项目协作者列表
     */
    @GetMapping("/{projectId}/collaborators")
    fun getProjectCollaborators(@PathVariable projectId: Long): Result<List<CollaboratorResponse>> {
        val userId = StpUtil.getLoginIdAsLong()
        
        // 检查访问权限
        if (!projectService.hasProjectAccess(projectId, userId)) {
            return Result.error("无权限访问该项目", 403)
        }
        
        val collaborators = projectService.getProjectCollaborators(projectId)
        return Result.success(collaborators)
    }

    /**
     * 添加项目协作者
     */
    @PostMapping("/{projectId}/collaborators")
    fun addCollaborator(
        @PathVariable projectId: Long,
        @Valid @RequestBody request: AddCollaboratorRequest
    ): Result<Nothing> {
        val userId = StpUtil.getLoginIdAsLong()
        projectService.addCollaborator(projectId, request.userId, request.role, userId)
        return Result.success(message = "协作者添加成功")
    }

    /**
     * 更新协作者角色
     */
    @PutMapping("/{projectId}/collaborators/{collaboratorUserId}")
    fun updateCollaborator(
        @PathVariable projectId: Long,
        @PathVariable collaboratorUserId: Long,
        @Valid @RequestBody request: UpdateCollaboratorRequest
    ): Result<Nothing> {
        val userId = StpUtil.getLoginIdAsLong()
        projectService.updateCollaborator(projectId, collaboratorUserId, request.role, userId)
        return Result.success(message = "协作者角色更新成功")
    }

    /**
     * 移除协作者
     */
    @DeleteMapping("/{projectId}/collaborators/{collaboratorUserId}")
    fun removeCollaborator(
        @PathVariable projectId: Long,
        @PathVariable collaboratorUserId: Long
    ): Result<Nothing> {
        val userId = StpUtil.getLoginIdAsLong()
        projectService.removeCollaborator(projectId, collaboratorUserId, userId)
        return Result.success(message = "协作者移除成功")
    }

    /**
     * 接受协作邀请
     */
    @PostMapping("/{projectId}/collaborators/accept")
    fun acceptInvitation(@PathVariable projectId: Long): Result<Nothing> {
        val userId = StpUtil.getLoginIdAsLong()
        projectService.acceptCollaboratorInvitation(projectId, userId)
        return Result.success(message = "邀请接受成功")
    }

    /**
     * 拒绝协作邀请
     */
    @PostMapping("/{projectId}/collaborators/reject")
    fun rejectInvitation(@PathVariable projectId: Long): Result<Nothing> {
        val userId = StpUtil.getLoginIdAsLong()
        projectService.rejectCollaboratorInvitation(projectId, userId)
        return Result.success(message = "邀请拒绝成功")
    }

    /**
     * 获取用户的项目权限
     */
    @GetMapping("/{projectId}/permissions")
    fun getUserProjectPermissions(@PathVariable projectId: Long): Result<Map<String, Any>> {
        val userId = StpUtil.getLoginIdAsLong()
        val userRole = projectService.getUserProjectRole(projectId, userId)
        
        if (userRole == null) {
            return Result.error("无权限访问该项目", 403)
        }
        
        val permissions = mapOf(
            "role" to userRole,
            "canEdit" to projectService.checkUserPermission(projectId, userId, "EDIT"),
            "canDelete" to projectService.checkUserPermission(projectId, userId, "DELETE"),
            "canManage" to projectService.checkUserPermission(projectId, userId, "MANAGE")
        )
        
        return Result.success(permissions)
    }

    /**
     * 更新项目最后访问时间
     */
    @PostMapping("/{projectId}/access")
    fun updateLastAccessTime(@PathVariable projectId: Long): Result<Nothing> {
        val userId = StpUtil.getLoginIdAsLong()
        
        // 检查访问权限
        if (!projectService.hasProjectAccess(projectId, userId)) {
            return Result.error("无权限访问该项目", 403)
        }
        
        projectService.updateLastAccessTime(projectId)
        return Result.success(message = "访问时间更新成功")
    }
}
