package com.outbook.maliang.enums

/**
 * 项目角色枚举
 */
enum class ProjectRole(val code: String, val description: String) {
    /**
     * 项目所有者 - 拥有所有权限
     */
    OWNER("OWNER", "项目所有者"),
    
    /**
     * 编辑者 - 可以编辑项目内容
     */
    EDITOR("EDITOR", "编辑者"),
    
    /**
     * 查看者 - 只能查看项目内容
     */
    VIEWER("VIEWER", "查看者");
    
    companion object {
        /**
         * 根据代码获取角色
         */
        fun fromCode(code: String): ProjectRole? {
            return values().find { it.code == code }
        }
        
        /**
         * 检查角色是否有编辑权限
         */
        fun hasEditPermission(role: String): Bo<PERSON>an {
            return role == OWNER.code || role == EDITOR.code
        }
        
        /**
         * 检查角色是否有管理权限（添加/删除协作者）
         */
        fun hasManagePermission(role: String): <PERSON><PERSON><PERSON> {
            return role == OWNER.code
        }
        
        /**
         * 检查角色是否有删除项目权限
         */
        fun hasDeletePermission(role: String): Boolean {
            return role == OWNER.code
        }
    }
}
