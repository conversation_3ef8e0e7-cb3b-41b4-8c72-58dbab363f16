package com.outbook.maliang.enums

/**
 * 协作者状态枚举
 */
enum class CollaboratorStatus(val code: String, val description: String) {
    /**
     * 待接受邀请
     */
    PENDING("PENDING", "待接受"),
    
    /**
     * 已接受邀请
     */
    ACCEPTED("ACCEPTED", "已接受"),
    
    /**
     * 已拒绝邀请
     */
    REJECTED("REJECTED", "已拒绝");
    
    companion object {
        /**
         * 根据代码获取状态
         */
        fun fromCode(code: String): CollaboratorStatus? {
            return values().find { it.code == code }
        }
    }
}
