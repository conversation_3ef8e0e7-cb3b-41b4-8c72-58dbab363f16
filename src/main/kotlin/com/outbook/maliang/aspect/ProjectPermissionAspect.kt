package com.outbook.maliang.aspect

import cn.dev33.satoken.stp.StpUtil
import com.outbook.maliang.annotation.ProjectPermission
import com.outbook.maliang.common.exception.BusinessException
import com.outbook.maliang.service.impl.ProjectServiceImpl
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.stereotype.Component
import java.lang.reflect.Parameter

/**
 * 项目权限检查切面
 */
@Aspect
@Component
class ProjectPermissionAspect(
    private val projectService: ProjectServiceImpl
) {

    @Around("@annotation(projectPermission)")
    fun checkProjectPermission(joinPoint: ProceedingJoinPoint, projectPermission: ProjectPermission): Any? {
        // 获取当前用户ID
        val userId = try {
            StpUtil.getLoginIdAsLong()
        } catch (e: Exception) {
            throw BusinessException("用户未登录", 401)
        }

        // 获取方法参数
        val method = (joinPoint.signature as MethodSignature).method
        val parameterNames = method.parameters.map { it.name }
        val args = joinPoint.args

        // 查找项目ID参数
        val projectIdIndex = parameterNames.indexOf(projectPermission.projectIdParam)
        if (projectIdIndex == -1) {
            throw BusinessException("未找到项目ID参数: ${projectPermission.projectIdParam}", 500)
        }

        val projectId = args[projectIdIndex] as? Long
            ?: throw BusinessException("项目ID参数类型错误", 400)

        // 检查权限
        val hasPermission = when (projectPermission.value) {
            "VIEW" -> projectService.hasProjectAccess(projectId, userId)
            "EDIT" -> projectService.checkUserPermission(projectId, userId, "EDIT")
            "MANAGE" -> projectService.checkUserPermission(projectId, userId, "MANAGE")
            "DELETE" -> projectService.checkUserPermission(projectId, userId, "DELETE")
            else -> false
        }

        if (!hasPermission) {
            throw BusinessException("无权限执行该操作", 403)
        }

        // 执行原方法
        return joinPoint.proceed()
    }
}
