package com.outbook.maliang.dto.request

import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

/**
 * 创建项目请求DTO
 */
data class CreateProjectRequest(
    @field:NotBlank(message = "项目名称不能为空")
    @field:Size(min = 1, max = 200, message = "项目名称长度必须在1-200个字符之间")
    val name: String,
    
    @field:Size(max = 1000, message = "项目描述长度不能超过1000个字符")
    val description: String? = null,
    
    val visibility: String = "PRIVATE", // PUBLIC, PRIVATE, TEAM
    
    @field:Size(max = 500, message = "封面图URL长度不能超过500个字符")
    val coverUrl: String? = null
)

/**
 * 更新项目请求DTO
 */
data class UpdateProjectRequest(
    @field:Size(min = 1, max = 200, message = "项目名称长度必须在1-200个字符之间")
    val name: String? = null,
    
    @field:Size(max = 1000, message = "项目描述长度不能超过1000个字符")
    val description: String? = null,
    
    val visibility: String? = null, // PUBLIC, PRIVATE, TEAM
    
    @field:Size(max = 500, message = "封面图URL长度不能超过500个字符")
    val coverUrl: String? = null
)

/**
 * 添加协作者请求DTO
 */
data class AddCollaboratorRequest(
    @field:NotBlank(message = "用户ID不能为空")
    val userId: Long,
    
    @field:NotBlank(message = "角色不能为空")
    val role: String, // OWNER, EDITOR, VIEWER
    
    val permissions: String? = null // JSON格式的权限配置
)

/**
 * 更新协作者请求DTO
 */
data class UpdateCollaboratorRequest(
    @field:NotBlank(message = "角色不能为空")
    val role: String, // OWNER, EDITOR, VIEWER
    
    val permissions: String? = null // JSON格式的权限配置
)

/**
 * 项目查询请求DTO
 */
data class ProjectQueryRequest(
    val page: Int = 1,
    val size: Int = 10,
    val keyword: String? = null,
    val visibility: String? = null,
    val status: String? = null,
    val sortBy: String = "updatedAt", // name, createdAt, updatedAt
    val sortOrder: String = "desc" // asc, desc
)
