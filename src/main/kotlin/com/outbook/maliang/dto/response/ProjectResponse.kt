package com.outbook.maliang.dto.response

import com.outbook.maliang.entity.Project
import java.time.LocalDateTime

/**
 * 项目响应DTO
 */
data class ProjectResponse(
    val id: Long,
    val name: String,
    val description: String?,
    val ownerId: Long,
    val ownerName: String?,
    val coverUrl: String?,
    val status: String,
    val visibility: String,
    val settings: String?,
    val lastAccessedAt: LocalDateTime?,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?,
    val userRole: String?, // 当前用户在该项目中的角色
    val collaboratorCount: Int = 0, // 协作者数量
    val canvasCount: Int = 0 // 画布数量
) {
    companion object {
        /**
         * 从Project实体转换为ProjectResponse
         */
        fun fromEntity(project: Project, ownerName: String? = null, userRole: String? = null): ProjectResponse {
            return ProjectResponse(
                id = project.id!!,
                name = project.name,
                description = project.description,
                ownerId = project.ownerId,
                ownerName = ownerName,
                coverUrl = project.coverUrl,
                status = project.status,
                visibility = project.visibility,
                settings = project.settings,
                lastAccessedAt = project.lastAccessedAt,
                createdAt = project.createdAt,
                updatedAt = project.updatedAt,
                userRole = userRole
            )
        }
    }
}

/**
 * 项目列表响应DTO
 */
data class ProjectListResponse(
    val records: List<ProjectResponse>,
    val total: Long,
    val size: Long,
    val current: Long,
    val pages: Long
)

/**
 * 协作者响应DTO
 */
data class CollaboratorResponse(
    val id: Long,
    val userId: Long,
    val username: String?,
    val nickname: String?,
    val email: String?,
    val avatarUrl: String?,
    val role: String,
    val permissions: String?,
    val status: String,
    val invitedBy: Long?,
    val invitedByName: String?,
    val joinedAt: LocalDateTime?,
    val createdAt: LocalDateTime?
)

/**
 * 项目详情响应DTO（包含协作者信息）
 */
data class ProjectDetailResponse(
    val project: ProjectResponse,
    val collaborators: List<CollaboratorResponse>,
    val permissions: ProjectPermissions
)

/**
 * 项目权限响应DTO
 */
data class ProjectPermissions(
    val canEdit: Boolean,
    val canDelete: Boolean,
    val canManageCollaborators: Boolean,
    val canCreateCanvas: Boolean,
    val canViewCollaborators: Boolean
)
