<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.outbook.maliang.mapper.ProjectCollaboratorMapper">

    <!-- 根据项目ID和用户ID查找协作者 -->
    <select id="findByProjectIdAndUserId" resultType="com.outbook.maliang.entity.ProjectCollaborator">
        SELECT * FROM project_collaborators 
        WHERE project_id = #{projectId} AND user_id = #{userId} AND deleted = 0
    </select>

    <!-- 获取项目的所有协作者（包含用户信息） -->
    <select id="selectProjectCollaboratorsWithUserInfo" resultType="map">
        SELECT 
            pc.id,
            pc.project_id,
            pc.user_id,
            pc.role,
            pc.permissions,
            pc.status,
            pc.invited_by,
            pc.joined_at,
            pc.created_at,
            u.username,
            u.nickname,
            u.email,
            u.avatar_url,
            inviter.username as invited_by_name
        FROM project_collaborators pc
        LEFT JOIN users u ON pc.user_id = u.id
        LEFT JOIN users inviter ON pc.invited_by = inviter.id
        WHERE pc.project_id = #{projectId} AND pc.deleted = 0
        ORDER BY pc.created_at ASC
    </select>

    <!-- 获取用户协作的项目ID列表 -->
    <select id="selectUserCollaboratedProjectIds" resultType="long">
        SELECT DISTINCT project_id 
        FROM project_collaborators 
        WHERE user_id = #{userId} AND status = 'ACCEPTED' AND deleted = 0
    </select>

    <!-- 统计项目的协作者数量 -->
    <select id="countProjectCollaborators" resultType="int">
        SELECT COUNT(*) 
        FROM project_collaborators 
        WHERE project_id = #{projectId} AND status = 'ACCEPTED' AND deleted = 0
    </select>

    <!-- 删除项目的所有协作者 -->
    <update id="deleteByProjectId">
        UPDATE project_collaborators 
        SET deleted = 1, updated_at = NOW() 
        WHERE project_id = #{projectId}
    </update>

    <!-- 更新协作者状态 -->
    <update id="updateCollaboratorStatus">
        UPDATE project_collaborators 
        SET status = #{status}, updated_at = NOW()
        <if test="status == 'ACCEPTED'">
            , joined_at = NOW()
        </if>
        WHERE project_id = #{projectId} AND user_id = #{userId} AND deleted = 0
    </update>

    <!-- 检查用户是否是项目协作者 -->
    <select id="isProjectCollaborator" resultType="boolean">
        SELECT COUNT(*) > 0 
        FROM project_collaborators 
        WHERE project_id = #{projectId} AND user_id = #{userId} 
        AND status = 'ACCEPTED' AND deleted = 0
    </select>

</mapper>
