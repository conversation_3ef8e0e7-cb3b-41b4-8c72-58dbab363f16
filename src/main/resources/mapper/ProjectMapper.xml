<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.outbook.maliang.mapper.ProjectMapper">

    <!-- 分页查询用户可访问的项目 -->
    <select id="selectUserAccessibleProjects" resultType="com.outbook.maliang.entity.Project">
        SELECT DISTINCT p.*,
               CASE
                   WHEN p.owner_id = #{userId} THEN 'OWNER'
                   ELSE pc.role
               END as user_role
        FROM projects p
        LEFT JOIN project_collaborators pc ON p.id = pc.project_id AND pc.user_id = #{userId} AND pc.status = 'ACCEPTED' AND pc.deleted = 0
        WHERE (p.owner_id = #{userId} OR pc.user_id = #{userId})
        AND p.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (p.name LIKE CONCAT('%', #{keyword}, '%') OR p.description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY p.updated_at DESC
    </select>

    <!-- 获取项目详情（包含用户角色） -->
    <select id="selectProjectWithUserRole" resultType="map">
        SELECT p.*,
               u.username as owner_name,
               CASE
                   WHEN p.owner_id = #{userId} THEN 'OWNER'
                   ELSE pc.role
               END as user_role
        FROM projects p
        LEFT JOIN users u ON p.owner_id = u.id
        LEFT JOIN project_collaborators pc ON p.id = pc.project_id AND pc.user_id = #{userId} AND pc.status = 'ACCEPTED' AND pc.deleted = 0
        WHERE p.id = #{projectId} AND p.deleted = 0
    </select>

    <!-- 检查用户对项目的访问权限 -->
    <select id="checkUserProjectAccess" resultType="string">
        SELECT
            CASE
                WHEN p.owner_id = #{userId} THEN 'OWNER'
                WHEN pc.role IS NOT NULL THEN pc.role
                ELSE NULL
            END as role
        FROM projects p
        LEFT JOIN project_collaborators pc ON p.id = pc.project_id
            AND pc.user_id = #{userId}
            AND pc.status = 'ACCEPTED'
            AND pc.deleted = 0
        WHERE p.id = #{projectId} AND p.deleted = 0
    </select>

    <!-- 查询用户拥有的项目 -->
    <select id="selectUserOwnedProjects" parameterType="long" resultType="com.outbook.maliang.entity.Project">
        SELECT * FROM projects WHERE owner_id = #{userId} AND deleted = 0 ORDER BY updated_at DESC
    </select>

    <!-- 查询用户协作的项目 -->
    <select id="selectUserCollaboratedProjects" parameterType="long" resultType="com.outbook.maliang.entity.Project">
        SELECT p.* FROM projects p
        INNER JOIN project_collaborators pc ON p.id = pc.project_id
        WHERE pc.user_id = #{userId} AND pc.status = 'ACCEPTED' AND p.deleted = 0
        ORDER BY p.updated_at DESC
    </select>

    <!-- 更新项目最后访问时间 -->
    <update id="updateLastAccessedTime" parameterType="long">
        UPDATE projects SET last_accessed_at = NOW() WHERE id = #{projectId}
    </update>

</mapper>
